image: maven:3.9.6-eclipse-temurin-21

stages:
  - compile
  - unit-test
  - package
  - acceptance-test-ref
  - acceptance-test-main
  - docker-build
  - docker-test
  - release
  - docker-publish

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

before_script:
  - apt-get update && apt-get install -y make docker.io
  - git config --global user.email "$GITLAB_USER_EMAIL"
  - git config --global user.name "$GITLAB_USER_NAME"


compile:
  stage: compile
  script:
    - make clean
    - make build
  artifacts:
    paths:
      - target/


unit_tests:
  stage: unit-test
  script:
    - make test-unittest
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/

package:
  stage: package
  script:
    - make build
  artifacts:
    paths:
      - target/*.jar


acceptance_tests_ref:
  stage: acceptance-test-ref
  script:
    - make test-iteration1
  allow_failure: false

acceptance_tests_main:
  stage: acceptance-test-main
  script:
    - make test-iteration2
  allow_failure: false


release:
  stage: release
  script:
    - make release
  artifacts:
    paths:
      - libs/my-server-*.jar
  dependencies:
    - acceptance_tests_ref
    - acceptance_tests_main


docker-build:
  stage: docker-build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - apk add --no-cache make
    - docker info
  script:
    - make docker-build
  artifacts:
    paths:
      - "robot-worlds-server-*.tar"
    expire_in: 1 month
  dependencies:
    - package


docker-test:
  stage: docker-test
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - apk add --no-cache make netcat-openbsd
    - docker info
  script:
    - make docker-test
  dependencies:
    - docker-build


docker-publish:
  stage: docker-publish
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - apk add --no-cache make
    - docker info
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - make docker-publish
  dependencies:
    - docker-build
    - docker-test


tag:
  stage: release
  script:
    - make tag
  dependencies:
    - release
  when: manual

