#!/usr/bin/env python3

import re
import sys

def test_shell_syntax():
    """Test the shell conditional syntax in the Makefile."""
    
    print("🔧 TESTING SHELL CONDITIONAL SYNTAX")
    print("=" * 50)
    
    try:
        with open('Makefile', 'r') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ ERROR: Makefile not found!")
        return False
    
    # Extract all shell conditionals
    shell_conditionals = re.findall(r'@if \[ "\$\(OS\)" = "windows" \]; then \\(.*?)fi', content, re.DOTALL)
    
    print(f"Found {len(shell_conditionals)} shell conditional blocks")
    
    syntax_issues = []
    
    for i, conditional in enumerate(shell_conditionals, 1):
        print(f"\nChecking conditional block {i}...")
        
        # Check for proper line continuation
        lines = conditional.split('\n')
        for j, line in enumerate(lines):
            line = line.strip()
            if line and not line.endswith('\\') and not line.endswith(';') and j < len(lines) - 1:
                next_line = lines[j + 1].strip() if j + 1 < len(lines) else ""
                if next_line and not next_line.startswith('else') and not next_line.startswith('fi'):
                    syntax_issues.append(f"Block {i}, line {j+1}: Missing line continuation or semicolon")
        
        # Check for proper else block
        if 'else \\' in conditional:
            print(f"   ✅ Block {i}: Has else block")
        else:
            syntax_issues.append(f"Block {i}: Missing else block")
        
        # Check for balanced structure
        if_count = conditional.count('if [')
        else_count = conditional.count('else')
        
        if if_count == 1 and else_count == 1:
            print(f"   ✅ Block {i}: Balanced if/else structure")
        else:
            syntax_issues.append(f"Block {i}: Unbalanced if/else (if: {if_count}, else: {else_count})")
    
    # Test the actual shell syntax by simulating execution
    print(f"\n🧪 SIMULATING SHELL EXECUTION")
    print("-" * 30)
    
    # Simulate Linux environment
    os_value = "linux"
    condition_result = os_value == "windows"
    
    print(f"OS value: '{os_value}'")
    print(f"Condition [ '{os_value}' = 'windows' ]: {condition_result}")
    print(f"Expected execution path: {'Windows (if)' if condition_result else 'Linux (else)'}")
    
    if not condition_result:
        print("✅ Will execute Linux commands as expected")
    else:
        print("❌ Would execute Windows commands (unexpected)")
    
    # Check for common shell syntax errors
    print(f"\n🔍 CHECKING FOR COMMON SYNTAX ERRORS")
    print("-" * 40)
    
    common_errors = [
        (r'if \[ "[^"]*" = "[^"]*" \]', "Missing 'then' after if condition"),
        (r'else[^\\]', "Missing backslash after else"),
        (r'[^;]\\$', "Line continuation without semicolon"),
    ]
    
    for pattern, error_desc in common_errors:
        matches = re.findall(pattern, content)
        if matches:
            syntax_issues.append(f"Potential syntax error: {error_desc} (found {len(matches)} instances)")
    
    # Final assessment
    print(f"\n" + "=" * 50)
    print("SHELL SYNTAX TEST RESULTS")
    print("=" * 50)
    
    if syntax_issues:
        print("❌ SYNTAX ISSUES FOUND:")
        for issue in syntax_issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ ALL SHELL SYNTAX CHECKS PASSED!")
        print("\nKey validations:")
        print("  ✅ Proper shell conditional structure")
        print("  ✅ Balanced if/else blocks")
        print("  ✅ Correct line continuation")
        print("  ✅ Linux execution path verified")
        print("\n🎉 Shell conditionals are properly implemented!")
        return True

if __name__ == "__main__":
    success = test_shell_syntax()
    sys.exit(0 if success else 1)
