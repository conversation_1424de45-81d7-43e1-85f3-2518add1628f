#!/bin/bash

# Test script to validate Makefile OS detection logic
echo "Testing Makefile OS detection..."

# Simulate Linux environment
export UNAME_S="Linux"
echo "Simulating Linux environment: UNAME_S=$UNAME_S"

# Test the OS detection logic from <PERSON><PERSON>le
if [ "$UNAME_S" = "Linux" ]; then
    OS="linux"
elif [ "$UNAME_S" = "Darwin" ]; then
    OS="macos"
else
    OS="windows"
fi

echo "Detected OS: $OS"

# Test the commands that would be used in Linux
if [ "$OS" = "linux" ]; then
    echo "Linux commands that would be used:"
    echo "  - pkill -f \"MultiServers\" 2>/dev/null || true"
    echo "  - nohup mvn exec:java ... > server.log 2>&1 &"
    echo "  - echo \$! > server.pid"
    echo "  - if [ -f server.pid ]; then kill \`cat server.pid\` 2>/dev/null || true; rm -f server.pid; fi"
else
    echo "Windows commands that would be used:"
    echo "  - taskkill //f //im java.exe 2>/dev/null || true"
    echo "  - start //b mvn exec:java ..."
fi

echo "Makefile OS detection test completed successfully!"
