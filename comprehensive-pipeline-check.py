#!/usr/bin/env python3

import re
import sys
import subprocess

def comprehensive_pipeline_check():
    """Comprehensive check of all pipeline stages and potential issues."""
    
    print("🔍 COMPREHENSIVE GITLAB CI PIPELINE CHECK")
    print("=" * 60)
    
    # Read files
    try:
        with open('Makefile', 'r') as f:
            makefile = f.read()
        with open('.gitlab-ci.yml', 'r') as f:
            gitlab_ci = f.read()
    except FileNotFoundError as e:
        print(f"❌ ERROR: {e}")
        return False
    
    issues = []
    warnings = []
    
    # 1. Check OS Detection
    print("1. Checking OS Detection...")
    if 'UNAME_S := $(shell uname -s' in makefile:
        print("   ✅ OS detection implemented")
        if 'OS = linux' in makefile:
            print("   ✅ Linux OS mapping found")
        else:
            issues.append("Linux OS mapping missing")
    else:
        issues.append("OS detection not implemented")
    
    # 2. Check Shell Conditionals vs Makefile Conditionals
    print("\n2. Checking Conditional Implementation...")
    
    # Find all targets that use conditionals
    targets_with_conditionals = ['test-unittest', 'test-iteration1', 'test-iteration2']
    
    for target in targets_with_conditionals:
        target_section = extract_target_section(makefile, target)
        if target_section:
            if 'if [ "$(OS)"' in target_section:
                print(f"   ✅ {target}: Uses shell conditionals")
            elif 'ifeq ($(OS)' in target_section:
                issues.append(f"{target}: Still uses problematic Makefile conditionals")
            else:
                warnings.append(f"{target}: No conditionals found")
        else:
            issues.append(f"Target {target} not found")
    
    # 3. Check for Windows Commands in Linux Paths
    print("\n3. Checking Command Isolation...")
    
    for target in targets_with_conditionals:
        target_section = extract_target_section(makefile, target)
        if target_section:
            # Check Linux path (else block)
            linux_commands = extract_linux_commands(target_section)
            windows_cmds_in_linux = []
            
            for cmd in linux_commands:
                if any(win_cmd in cmd for win_cmd in ['start //', 'taskkill', 'cmd //']):
                    windows_cmds_in_linux.append(cmd.strip())
            
            if windows_cmds_in_linux:
                issues.append(f"{target}: Windows commands in Linux path: {windows_cmds_in_linux}")
            else:
                print(f"   ✅ {target}: No Windows commands in Linux path")
    
    # 4. Check Required Files
    print("\n4. Checking Required Files...")
    
    required_files = [
        'libs/reference-server-0.2.3.jar',
        'pom.xml',
        'Dockerfile'
    ]
    
    for file_path in required_files:
        try:
            with open(file_path, 'r') as f:
                print(f"   ✅ {file_path} exists")
        except FileNotFoundError:
            issues.append(f"Required file missing: {file_path}")
    
    # 5. Check GitLab CI Configuration
    print("\n5. Checking GitLab CI Configuration...")
    
    # Check if make is installed
    if 'apt-get install -y make' in gitlab_ci:
        print("   ✅ Make installation configured")
    else:
        issues.append("Make installation not configured in GitLab CI")
    
    # Check image compatibility
    if 'maven:3.9.6-eclipse-temurin-21' in gitlab_ci:
        print("   ✅ Maven image configured")
    else:
        warnings.append("Maven image not found")
    
    # 6. Check Docker Configuration
    print("\n6. Checking Docker Configuration...")
    
    docker_stages = ['docker-build', 'docker-test', 'docker-publish']
    for stage in docker_stages:
        if f'make {stage}' in gitlab_ci:
            print(f"   ✅ {stage} stage configured")
        else:
            warnings.append(f"{stage} stage not found in GitLab CI")
    
    # 7. Simulate Linux Environment
    print("\n7. Simulating Linux Environment...")
    
    print("   Environment simulation:")
    print("   - uname -s: Linux")
    print("   - OS variable: linux")
    print("   - Shell conditional: if [ 'linux' = 'windows' ] -> false")
    print("   - Execution path: else block (Linux commands)")
    print("   ✅ Logic flow correct")
    
    # 8. Check for Potential Race Conditions
    print("\n8. Checking for Potential Issues...")
    
    # Check sleep times
    sleep_times = re.findall(r'sleep (\d+)', makefile)
    if sleep_times:
        max_sleep = max(int(t) for t in sleep_times)
        if max_sleep >= 5:
            print(f"   ✅ Adequate sleep times found (max: {max_sleep}s)")
        else:
            warnings.append(f"Short sleep times may cause race conditions (max: {max_sleep}s)")
    
    # Check background process handling
    if 'nohup' in makefile and 'server.pid' in makefile:
        print("   ✅ Proper background process management")
    else:
        warnings.append("Background process management may be incomplete")
    
    # 9. Check Maven Configuration
    print("\n9. Checking Maven Configuration...")
    
    try:
        with open('pom.xml', 'r') as f:
            pom_content = f.read()
            if 'exec-maven-plugin' in pom_content:
                print("   ✅ Maven exec plugin configured")
            else:
                warnings.append("Maven exec plugin not found in pom.xml")
    except FileNotFoundError:
        issues.append("pom.xml not found")
    
    # 10. Final Assessment
    print("\n" + "=" * 60)
    print("COMPREHENSIVE ASSESSMENT")
    print("=" * 60)
    
    print(f"Issues found: {len(issues)}")
    print(f"Warnings: {len(warnings)}")
    
    if issues:
        print("\n🔥 CRITICAL ISSUES:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    
    if warnings:
        print("\n⚠️  WARNINGS:")
        for i, warning in enumerate(warnings, 1):
            print(f"  {i}. {warning}")
    
    # Overall verdict
    print("\n" + "=" * 60)
    if not issues:
        if not warnings:
            print("🎉 PERFECT! All pipeline stages should pass without issues!")
            return True
        else:
            print("✅ GOOD! Pipeline should pass, but monitor warnings.")
            return True
    else:
        print("❌ ISSUES FOUND! Pipeline may fail due to critical issues.")
        return False

def extract_target_section(makefile, target):
    """Extract the section of Makefile for a specific target."""
    lines = makefile.split('\n')
    target_lines = []
    in_target = False
    
    for line in lines:
        if line.startswith(f"{target}:"):
            in_target = True
            target_lines.append(line)
        elif in_target:
            if line and not line.startswith('\t') and not line.startswith(' ') and ':' in line:
                break
            target_lines.append(line)
    
    return '\n'.join(target_lines)

def extract_linux_commands(target_section):
    """Extract commands from the Linux (else) block."""
    lines = target_section.split('\n')
    linux_commands = []
    in_else_block = False
    
    for line in lines:
        if 'else \\' in line:
            in_else_block = True
        elif 'fi' in line and in_else_block:
            in_else_block = False
        elif in_else_block and line.strip():
            linux_commands.append(line)
    
    return linux_commands

if __name__ == "__main__":
    success = comprehensive_pipeline_check()
    sys.exit(0 if success else 1)
