#!/usr/bin/env python3

import re
import sys

def test_pipeline_stages():
    """Test if all GitLab CI pipeline stages will pass with the updated Makefile."""
    
    print("Testing GitLab CI Pipeline Stage Compatibility")
    print("=" * 60)
    
    # Read GitLab CI configuration
    try:
        with open('.gitlab-ci.yml', 'r') as f:
            gitlab_ci = f.read()
    except FileNotFoundError:
        print("❌ ERROR: .gitlab-ci.yml not found!")
        return False
    
    # Read Makefile
    try:
        with open('Makefile', 'r') as f:
            makefile = f.read()
    except FileNotFoundError:
        print("❌ ERROR: Makefile not found!")
        return False
    
    # Extract make commands from GitLab CI
    make_commands = re.findall(r'- make (\S+)', gitlab_ci)
    print(f"Found make commands in GitLab CI: {make_commands}")
    print()
    
    # Check each pipeline stage
    stages = [
        {
            'name': 'compile',
            'commands': ['clean', 'build'],
            'image': 'maven:3.9.6-eclipse-temurin-21',
            'critical': True
        },
        {
            'name': 'unit-test', 
            'commands': ['test-unittest'],
            'image': 'maven:3.9.6-eclipse-temurin-21',
            'critical': True
        },
        {
            'name': 'package',
            'commands': ['build'],
            'image': 'maven:3.9.6-eclipse-temurin-21', 
            'critical': True
        },
        {
            'name': 'acceptance-test-ref',
            'commands': ['test-iteration1'],
            'image': 'maven:3.9.6-eclipse-temurin-21',
            'critical': True
        },
        {
            'name': 'acceptance-test-main',
            'commands': ['test-iteration2'], 
            'image': 'maven:3.9.6-eclipse-temurin-21',
            'critical': True
        },
        {
            'name': 'release',
            'commands': ['release'],
            'image': 'maven:3.9.6-eclipse-temurin-21',
            'critical': False
        },
        {
            'name': 'docker-build',
            'commands': ['docker-build'],
            'image': 'docker:24.0.5',
            'critical': False
        },
        {
            'name': 'docker-test',
            'commands': ['docker-test'],
            'image': 'docker:24.0.5', 
            'critical': False
        },
        {
            'name': 'docker-publish',
            'commands': ['docker-publish'],
            'image': 'docker:24.0.5',
            'critical': False
        },
        {
            'name': 'tag',
            'commands': ['tag'],
            'image': 'maven:3.9.6-eclipse-temurin-21',
            'critical': False
        }
    ]
    
    results = []
    
    for stage in stages:
        print(f"Testing stage: {stage['name']}")
        print(f"  Image: {stage['image']}")
        print(f"  Commands: {stage['commands']}")
        
        stage_issues = []
        
        # Check if all commands exist in Makefile
        for cmd in stage['commands']:
            if f"{cmd}:" in makefile:
                print(f"  ✅ Target '{cmd}' found in Makefile")
                
                # Check if target uses shell conditionals (Linux compatible)
                target_section = extract_target_section(makefile, cmd)
                if target_section:
                    if has_shell_conditionals(target_section):
                        print(f"     ✅ Uses shell conditionals (Linux compatible)")
                    elif has_makefile_conditionals(target_section):
                        stage_issues.append(f"Target '{cmd}' uses Makefile conditionals (may cause issues)")
                    else:
                        print(f"     ✅ No conditionals (should work on all platforms)")
                        
                    # Check for Windows-specific commands
                    if has_windows_commands(target_section):
                        if has_shell_conditionals(target_section):
                            print(f"     ✅ Windows commands properly isolated in conditionals")
                        else:
                            stage_issues.append(f"Target '{cmd}' has Windows commands without conditionals")
                    else:
                        print(f"     ✅ No Windows-specific commands")
            else:
                stage_issues.append(f"Target '{cmd}' not found in Makefile")
        
        # Stage-specific checks
        if stage['name'] in ['docker-build', 'docker-test', 'docker-publish']:
            print(f"  ℹ️  Docker stage - requires Docker environment")
            
        if stage_issues:
            print(f"  ❌ Issues found:")
            for issue in stage_issues:
                print(f"     - {issue}")
            results.append({'stage': stage['name'], 'status': 'FAIL', 'issues': stage_issues, 'critical': stage['critical']})
        else:
            print(f"  ✅ Stage should pass")
            results.append({'stage': stage['name'], 'status': 'PASS', 'issues': [], 'critical': stage['critical']})
        
        print()
    
    # Summary
    print("=" * 60)
    print("PIPELINE STAGE ANALYSIS SUMMARY")
    print("=" * 60)
    
    passed = [r for r in results if r['status'] == 'PASS']
    failed = [r for r in results if r['status'] == 'FAIL']
    critical_failed = [r for r in failed if r['critical']]
    
    print(f"Total stages: {len(results)}")
    print(f"Passing: {len(passed)}")
    print(f"Failing: {len(failed)}")
    print(f"Critical failures: {len(critical_failed)}")
    print()
    
    if failed:
        print("FAILING STAGES:")
        for result in failed:
            status_icon = "🔥" if result['critical'] else "⚠️"
            print(f"  {status_icon} {result['stage']} ({'CRITICAL' if result['critical'] else 'NON-CRITICAL'})")
            for issue in result['issues']:
                print(f"     - {issue}")
        print()
    
    if passed:
        print("PASSING STAGES:")
        for result in passed:
            print(f"  ✅ {result['stage']}")
        print()
    
    # Overall assessment
    if critical_failed:
        print("🔥 CRITICAL ISSUES FOUND - Pipeline will likely fail")
        return False
    elif failed:
        print("⚠️  Some non-critical stages may fail, but main pipeline should work")
        return True
    else:
        print("🎉 ALL STAGES SHOULD PASS!")
        return True

def extract_target_section(makefile, target):
    """Extract the section of Makefile for a specific target."""
    lines = makefile.split('\n')
    target_lines = []
    in_target = False
    
    for line in lines:
        if line.startswith(f"{target}:"):
            in_target = True
            target_lines.append(line)
        elif in_target:
            if line and not line.startswith('\t') and not line.startswith(' ') and ':' in line:
                break
            target_lines.append(line)
    
    return '\n'.join(target_lines)

def has_shell_conditionals(text):
    """Check if text contains shell conditionals."""
    return 'if [ "$(OS)"' in text

def has_makefile_conditionals(text):
    """Check if text contains Makefile conditionals."""
    return 'ifeq' in text or 'endif' in text

def has_windows_commands(text):
    """Check if text contains Windows-specific commands."""
    windows_cmds = ['taskkill', 'start //', 'cmd //', 'timeout /']
    return any(cmd in text for cmd in windows_cmds)

if __name__ == "__main__":
    success = test_pipeline_stages()
    sys.exit(0 if success else 1)
