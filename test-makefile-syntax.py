#!/usr/bin/env python3

import re
import sys

def test_makefile_syntax():
    """Test the Makefile for proper syntax after shell conditional conversion."""
    
    print("Testing Makefile syntax after shell conditional conversion...")
    print("=" * 60)
    
    try:
        with open('Makefile', 'r') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ ERROR: Makefile not found!")
        return False
    
    lines = content.split('\n')
    errors = []
    warnings = []
    
    # Test 1: Check for problematic ifeq/endif in recipe lines
    print("1. Checking for problematic ifeq/endif statements...")
    
    in_recipe = False
    for i, line in enumerate(lines, 1):
        if line and not line.startswith('\t') and not line.startswith(' ') and ':' in line:
            in_recipe = False
        elif line.startswith('\t'):
            in_recipe = True
        
        if in_recipe and (line.strip().startswith('ifeq') or line.strip().startswith('endif')):
            errors.append(f"Line {i}: Found ifeq/endif in recipe line: {line.strip()}")
    
    if not errors:
        print("   ✅ No problematic ifeq/endif statements found in recipes")
    
    # Test 2: Check for shell conditionals
    print("\n2. Checking for shell conditionals...")
    
    shell_conditionals = []
    for i, line in enumerate(lines, 1):
        if 'if [ "$(OS)"' in line:
            shell_conditionals.append(i)
    
    if shell_conditionals:
        print(f"   ✅ Found shell conditionals on lines: {shell_conditionals}")
    else:
        warnings.append("No shell conditionals found")
    
    # Test 3: Check for proper line continuation
    print("\n3. Checking for proper line continuation...")
    
    continuation_issues = []
    for i, line in enumerate(lines, 1):
        if line.strip().endswith('\\') and i < len(lines):
            next_line = lines[i] if i < len(lines) else ""
            if not next_line.startswith('\t\t'):
                continuation_issues.append(f"Line {i}: Line continuation may have incorrect indentation")
    
    if not continuation_issues:
        print("   ✅ Line continuation appears correct")
    else:
        for issue in continuation_issues:
            warnings.append(issue)
    
    # Test 4: Check for Windows commands in Linux paths
    print("\n4. Checking for Windows commands in shell conditionals...")
    
    windows_commands_in_linux = []
    in_else_block = False
    
    for i, line in enumerate(lines, 1):
        if 'else \\' in line:
            in_else_block = True
        elif 'fi' in line and in_else_block:
            in_else_block = False
        elif in_else_block:
            if any(cmd in line for cmd in ['taskkill', 'start //b', 'cmd //c']):
                windows_commands_in_linux.append(f"Line {i}: Windows command in Linux block: {line.strip()}")
    
    if not windows_commands_in_linux:
        print("   ✅ No Windows commands found in Linux execution paths")
    else:
        for issue in windows_commands_in_linux:
            errors.append(issue)
    
    # Test 5: Check for Linux commands in Windows paths
    print("\n5. Checking for Linux commands in shell conditionals...")
    
    linux_commands_in_windows = []
    in_if_block = False
    
    for i, line in enumerate(lines, 1):
        if 'if [ "$(OS)" = "windows"' in line:
            in_if_block = True
        elif 'else \\' in line and in_if_block:
            in_if_block = False
        elif in_if_block:
            if any(cmd in line for cmd in ['pkill', 'nohup', 'kill `cat']):
                linux_commands_in_windows.append(f"Line {i}: Linux command in Windows block: {line.strip()}")
    
    if not linux_commands_in_windows:
        print("   ✅ No Linux commands found in Windows execution paths")
    else:
        for issue in linux_commands_in_windows:
            errors.append(issue)
    
    # Test 6: Simulate what would happen in GitLab CI
    print("\n6. Simulating GitLab CI execution...")
    print("   Environment: Linux (uname -s = 'Linux')")
    print("   OS variable would be set to: 'linux'")
    print("   Shell conditional: if [ 'linux' = 'windows' ] -> false")
    print("   Execution path: else block (Linux commands)")
    print("   ✅ Should execute Linux commands correctly")
    
    # Print results
    print("\n" + "=" * 60)
    print("SYNTAX TEST RESULTS:")
    print(f"Errors: {len(errors)}")
    print(f"Warnings: {len(warnings)}")
    
    if errors:
        print("\nERRORS:")
        for error in errors:
            print(f"  ❌ {error}")
    
    if warnings:
        print("\nWARNINGS:")
        for warning in warnings:
            print(f"  ⚠️  {warning}")
    
    if not errors:
        print("\n✅ Makefile syntax test PASSED!")
        print("✅ The shell conditional approach should work in GitLab CI")
        return True
    else:
        print("\n❌ Makefile syntax test FAILED!")
        return False

if __name__ == "__main__":
    success = test_makefile_syntax()
    sys.exit(0 if success else 1)
