//package acceptance_test.Iteration2;
//
//import acceptance_test.launchhelpermethods.ServerCommunicator;
//import com.google.gson.JsonObject;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//
//import static acceptance_test.launchhelpermethods.LaunchRequestFactory.*;
//import static acceptance_test.launchhelpermethods.LaunchTestConstants.*;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
//public class LaunchAcceptanceTest {
//
//    // Scenario 1: Can launch another robot
//
//    @Test
//    @DisplayName("AT-10.1.1: Test Can Launch Another Robot")
//    void testCanLaunchAnotherRobot() throws Exception {
//        JsonObject firstLaunch = createValidLaunchRequestProtocol(SOLDIER_TYPE, "Test1", 3, 3);
//        JsonObject firstResponse = ServerCommunicator.sendLaunchRequest(firstLaunch);
//        assertEquals("OK", firstResponse.get("result").getAsString());
//
//        JsonObject secondLaunch = createValidLaunchRequestProtocol(SOLDIER_TYPE, "Test2", 5, 5);
//        JsonObject secondResponse = ServerCommunicator.sendLaunchRequest(secondLaunch);
//        assertEquals("OK", secondResponse.get("result").getAsString());
//    }
//
//    // Scenario 2: World without obstacles is full
//
//    @Test
//    @DisplayName("AT-10.1.2: Test World Without Obstacles Is Full")
//    void testWorldWithoutObstaclesIsFull() throws Exception {
//        for (int i = 0; i < 4; i++) {
//            JsonObject request = createValidLaunchRequestProtocol(SOLDIER_TYPE, "Robot" + i, 3, 3);
//            JsonObject response = ServerCommunicator.sendLaunchRequest(request);
//            assertEquals("OK", response.get("result").getAsString());
//        }
//
//        JsonObject finalRequest = createValidLaunchRequestProtocol(HITBOT_TYPE, "TestBot", 5, 5);
//        JsonObject finalResponse = ServerCommunicator.sendLaunchRequest(finalRequest);
//        assertEquals("OK", finalResponse.get("result").getAsString());
//    }
//
//    // Scenario 3: Launch robots into a world with an obstacle
//
//    @Test
//    @DisplayName("AT-10.1.3: Test Launch Into A World With An Obstacle")
//    void testLaunchIntoWorldWithObstacle() throws Exception {
//        JsonObject launchRequest = createValidLaunchRequestProtocol(SOLDIER_TYPE, "TestBot", 5, 5);
//        JsonObject response = ServerCommunicator.sendLaunchRequest(launchRequest);
//        assertEquals("OK", response.get("result").getAsString());
//    }
//
//    // Scenario 4: World with an obstacle is full
//
//    @Test
//    @DisplayName("AT-10.1.4: Test World With Obstacles Is Full")
//    void testWorldWithObstaclesIsFull() throws Exception {
//        for (int i = 0; i < 3; i++) {
//            JsonObject request = createValidLaunchRequestProtocol(SOLDIER_TYPE, "ObstacleBot" + i, 3, 3);
//            JsonObject response = ServerCommunicator.sendLaunchRequest(request);
//            assertEquals("OK", response.get("result").getAsString());
//        }
//    }
//}
