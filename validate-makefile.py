#!/usr/bin/env python3

import re
import sys

def validate_makefile():
    """Validate the Makefile for common syntax issues and OS detection logic."""
    
    print("Validating Makefile...")
    
    try:
        with open('Makefile', 'r') as f:
            content = f.read()
    except FileNotFoundError:
        print("ERROR: Makefile not found!")
        return False
    
    errors = []
    warnings = []
    
    # Check for OS detection
    if 'UNAME_S := $(shell uname -s' in content:
        print("✓ OS detection logic found")
    else:
        errors.append("OS detection logic not found")
    
    # Check for conditional blocks
    if_blocks = re.findall(r'ifeq \(\$\(OS\),(\w+)\)', content)
    has_windows_blocks = 'windows' in if_blocks
    has_else_blocks = 'else' in content and 'endif' in content

    if has_windows_blocks and has_else_blocks:
        print("✓ Both Windows and Linux conditional blocks found")
    elif has_windows_blocks:
        print("✓ Windows conditional blocks found")
        if not has_else_blocks:
            warnings.append("Linux blocks may be missing (no else blocks found)")
    else:
        errors.append("Missing Windows conditional blocks")
    
    # Check for Windows-specific commands in Windows blocks
    windows_sections = re.findall(r'ifeq \(\$\(OS\),windows\)(.*?)else', content, re.DOTALL)
    for section in windows_sections:
        if 'taskkill' in section and 'start' in section:
            print("✓ Windows-specific commands found in Windows blocks")
        else:
            warnings.append("Windows block may be missing Windows-specific commands")
    
    # Check for Linux-specific commands in else blocks
    else_sections = re.findall(r'else(.*?)endif', content, re.DOTALL)
    for section in else_sections:
        if 'pkill' in section and 'nohup' in section:
            print("✓ Linux-specific commands found in Linux blocks")
        else:
            warnings.append("Linux block may be missing Linux-specific commands")
    
    # Check for proper endif statements
    # Count independent ifeq blocks (not else ifeq which are part of the same block)
    ifeq_blocks = len(re.findall(r'^ifeq', content, re.MULTILINE))
    endif_count = content.count('endif')
    if ifeq_blocks == endif_count:
        print("✓ Balanced ifeq/endif statements")
    else:
        errors.append(f"Unbalanced ifeq/endif: {ifeq_blocks} ifeq blocks vs {endif_count} endif")
    
    # Check for tab indentation in recipe lines
    lines = content.split('\n')
    for i, line in enumerate(lines, 1):
        if line.startswith('\t@') or line.startswith('\t'):
            # This is a recipe line, which is correct
            continue
        elif line.startswith('    @') or (line.startswith('    ') and '@' in line):
            warnings.append(f"Line {i}: Recipe line may use spaces instead of tabs")
    
    # Print results
    print(f"\nValidation Results:")
    print(f"Errors: {len(errors)}")
    print(f"Warnings: {len(warnings)}")
    
    if errors:
        print("\nERRORS:")
        for error in errors:
            print(f"  - {error}")
    
    if warnings:
        print("\nWARNINGS:")
        for warning in warnings:
            print(f"  - {warning}")
    
    if not errors:
        print("\n✓ Makefile validation passed!")
        return True
    else:
        print("\n✗ Makefile validation failed!")
        return False

if __name__ == "__main__":
    success = validate_makefile()
    sys.exit(0 if success else 1)
